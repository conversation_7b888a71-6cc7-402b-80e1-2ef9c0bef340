# 🌍 Intelligent Environmental News Analytics Platform
### *A Cloud-Native AI-Powered Data Pipeline for Vietnamese Environmental Journalism*

<div align="center">

![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)
![Scrapy](https://img.shields.io/badge/Scrapy-2.13+-green.svg)
![AWS](https://img.shields.io/badge/AWS-Bedrock%20%7C%20ECS-orange.svg)
![GCP](https://img.shields.io/badge/GCP-Cloud%20Run%20%7C%20Vertex%20AI-blue.svg)
![Apache Airflow](https://img.shields.io/badge/Apache-Airflow-red.svg)
![Docker](https://img.shields.io/badge/Docker-Containerized-blue.svg)

*🏆 Advanced Seminar Project - Data Engineering & AI Analytics*

</div>

---

## 🚀 **Project Vision**

This project represents a **comprehensive end-to-end data engineering solution** that combines **web scraping**, **cloud computing**, **AI/ML**, and **workflow orchestration** to create an intelligent analytics platform for Vietnamese environmental journalism.

The system automatically **collects**, **processes**, **analyzes**, and **enriches** environmental news data using state-of-the-art AI models deployed across multiple cloud platforms.

---

## 🏗️ **System Architecture**

<div align="center">

```mermaid
graph TB
    A[Vietnamese News Sources] --> B[Scrapy + Playwright Spiders]
    B --> C[Raw Data Storage]
    C --> D[Apache Airflow Orchestration]
    D --> E[GCP Cloud Run Data Processing]
    E --> F[AWS Bedrock AI Analysis]
    F --> G[Vertex AI Model Fine-tuning]
    G --> H[Enriched Analytics Dashboard]

    subgraph "Data Sources"
        A1[VnExpress]
        A2[Thanh Niên]
        A3[Tuổi Trẻ]
        A4[VietnamNet]
        A5[Dân Trí]
    end

    subgraph "Cloud Infrastructure"
        E1[Data Cleaning Service]
        E2[Data Merging Service]
        F1[Claude 3 Haiku]
        F2[AWS ECS Deployment]
        G1[Custom Pollution Classification]
    end
```

</div>

---

## 🎯 **Core Capabilities**

### 📰 **Intelligent News Aggregation**
- **Multi-source scraping** from 5 major Vietnamese news platforms
- **Dynamic content handling** with Playwright browser automation
- **Real-time data extraction** with anti-bot protection mechanisms
- **Comprehensive metadata collection** including comments and social interactions

### ☁️ **Cloud-Native Processing Pipeline**
- **Google Cloud Run** microservices for scalable data processing
- **AWS Bedrock** integration for advanced AI text analysis
- **Apache Airflow** for robust workflow orchestration
- **Docker containerization** for consistent deployment environments

### 🤖 **AI-Powered Analytics**
- **Custom model fine-tuning** for pollution type classification
- **AWS Bedrock Claude 3** integration for content analysis
- **Google Vertex AI** for advanced machine learning workflows
- **Automated content enrichment** and sentiment analysis

---

## 🛠️ **Technology Stack**

<div align="center">

| **Category** | **Technologies** | **Purpose** |
|--------------|------------------|-------------|
| **🐍 Core** | Python 3.12+, Scrapy 2.13+, Playwright | Web scraping & automation |
| **☁️ Cloud Platforms** | AWS (Bedrock, ECS), GCP (Cloud Run, Vertex AI) | Scalable AI & compute services |
| **🔄 Orchestration** | Apache Airflow, Docker Compose | Workflow management |
| **🤖 AI/ML** | AWS Bedrock Claude 3, Google Vertex AI | Natural language processing |
| **📊 Data Processing** | Pandas, JSON processing, Cloud Storage | Data transformation & storage |
| **🚀 Deployment** | Docker, Cloud Run, ECS | Containerized microservices |

</div>

---

## 📋 **Prerequisites**

- **Python 3.12+** with modern package management
- **Docker & Docker Compose** for containerization
- **Cloud Platform Access**: AWS (Bedrock) & GCP (Cloud Run, Vertex AI)
- **uv package manager** (recommended) or pip

---

## 🚀 **Quick Start Guide**

### 🔧 **Local Development Setup**

```bash
# 1. Clone the repository
git clone <repository-url>
cd Scrape_papers

# 2. Install dependencies (using uv - recommended)
uv sync
uv run playwright install

# 3. Alternative: Using pip
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -e .
playwright install
```

### 🐳 **Docker Deployment**

```bash
# 1. Start Apache Airflow with PostgreSQL
cd airflow
docker-compose up -d

# 2. Deploy Cloud Run services
cd ../cloudRun/clean_data
docker build -t clean-data-service .
gcloud run deploy clean-data --image clean-data-service

cd ../merge_data
docker build -t merge-data-service .
gcloud run deploy merge-data --image merge-data-service

# 3. Configure AWS Bedrock credentials
cd ../../bedrocks-apis(ECS)
# Add your AWS credentials and deploy to ECS
```

### ⚡ **Quick Test Run**

```bash
# Test individual news scrapers
cd news_scraping
scrapy crawl vnexpress -o sample_data.json

# Test AI processing pipeline
cd ../bedrocks-apis(ECS)
python testBedrock.py

# Test GCP Vertex AI integration
python gcp.py
```

---

## 🎮 **Usage Examples**

### 📰 **News Scraping Operations**

<details>
<summary><b>🔍 Individual Spider Execution</b></summary>

```bash
cd news_scraping

# VnExpress - Vietnam's leading news source
scrapy crawl vnexpress -o vnexpress_articles.json
scrapy crawl vnexpressV2 -o vnexpress_v2_articles.json  # Optimized version

# Major Vietnamese newspapers
scrapy crawl thanhnien -o thanhnien_articles.json      # Thanh Niên
scrapy crawl tuoitrevn -o tuoitre_articles.json        # Tuổi Trẻ
scrapy crawl vietnamnet -o vietnamnet_articles.json    # VietnamNet
scrapy crawl dantri -o dantri_articles.json            # Dân Trí
```

</details>

<details>
<summary><b>📊 Advanced Output Formats</b></summary>

```bash
# Multiple output formats supported
scrapy crawl vnexpress -o articles.json    # JSON (structured)
scrapy crawl vnexpress -o articles.csv     # CSV (analysis-ready)
scrapy crawl vnexpress -o articles.xml     # XML (enterprise)
scrapy crawl vnexpress -o articles.jl      # JSON Lines (streaming)

# Custom configurations
scrapy crawl vnexpress -s DOWNLOAD_DELAY=2 -o articles.json
scrapy crawl vnexpress -s CONCURRENT_REQUESTS_PER_DOMAIN=2 -o articles.json
scrapy crawl vnexpress -L DEBUG -o debug_articles.json
```

</details>

### ☁️ **Cloud Processing Pipeline**

<details>
<summary><b>🔄 Data Processing Workflow</b></summary>

```bash
# 1. Clean raw scraped data using GCP Cloud Run
curl -X POST https://clean-data-service-url/clean \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "raw-news-data",
    "input_prefix": "scraped/",
    "output_bucket": "processed-news-data",
    "output_prefix": "cleaned/"
  }'

# 2. Merge multiple data sources
curl -X POST https://merge-data-service-url/merge \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "processed-news-data",
    "output_bucket": "merged-news-data"
  }'
```

</details>

### 🤖 **AI Analysis & Model Fine-tuning**

<details>
<summary><b>🧠 AWS Bedrock Integration</b></summary>

```bash
cd bedrocks-apis(ECS)

# Test AWS Bedrock Claude 3 Haiku connection
python testBedrock.py

# Run full AI analysis pipeline
python b.py  # Batch processing with Bedrock

# Deploy to AWS ECS for production
# Configure deployment.pem and push to ECS
```

</details>

<details>
<summary><b>🎯 Google Vertex AI Processing</b></summary>

```bash
# Process data with Google Vertex AI
python gcp.py

# Custom model fine-tuning for pollution classification
jupyter notebook modelFintuning/polutionTypeFituning.ipynb
```

</details>

## 📁 Project Structure

```
Scrape_papers/
├── main.py                    # Simple entry point
├── vnexpress.py              # Standalone VnExpress spider
├── pyproject.toml            # Project dependencies
├── uv.lock                   # Dependency lock file
├── README.md                 # This file
└── news_scraping/            # Main Scrapy project
    ├── scrapy.cfg            # Scrapy configuration
    └── news_scraping/
        ├── __init__.py
        ├── items.py          # Data models
        ├── middlewares.py    # Custom middlewares
        ├── pipelines.py      # Data processing pipelines
        ├── settings.py       # Scrapy settings
        ├── test.py          # Comment API testing
        ├── logs/            # Spider log files
        └── spiders/         # Spider implementations
            ├── __init__.py
            ├── vnexpress.py     # VnExpress spider v1
            ├── vnexpressV2.py   # VnExpress spider v2
            ├── thanhnien.py     # Thanh Niên spider
            ├── tuoitrevn.py     # Tuổi Trẻ spider
            ├── vietnamnet.py    # VietnamNet spider
            └── dantri.py        # Dân Trí spider
```

## ⚙️ How It Works

### 1. Search Strategy

Each spider uses different approaches to find environmental pollution articles:

- **VnExpress**: Scrapes from a dedicated environmental pollution topic page
- **Thanh Niên**: Uses search functionality with keyword "ô nhiễm môi trường"
- **Tuổi Trẻ**: Utilizes timeline search API with environmental keywords
- **VietnamNet**: Searches using encoded keywords through their search system
- **Dân Trí**: Searches for environmental pollution articles via their search interface

### 2. Data Extraction Process

1. **Article Discovery**: Each spider starts from search results or topic pages
2. **Link Following**: Extracts article URLs from listing pages
3. **Content Scraping**: Visits individual articles to extract detailed content
4. **Comment Collection**: Some spiders also collect user comments and interactions
5. **Data Validation**: Ensures data quality and completeness
6. **Output Generation**: Saves data in specified format (JSON, CSV, etc.)

### 3. Anti-Bot Measures

The project includes several features to handle anti-bot protection:

- **Rate Limiting**: Configurable download delays between requests
- **Concurrent Request Control**: Limits simultaneous requests per domain
- **Playwright Integration**: Handles JavaScript-heavy pages
- **Request Blocking**: Optimizes performance by blocking unnecessary resources
- **User Agent Rotation**: Can be configured for different user agents

### 4. Logging and Monitoring

- Each spider generates detailed logs in the `logs/` directory
- Log files are named by spider (e.g., `scrape_vnexpress_log.txt`)
- Configurable log levels (DEBUG, INFO, WARNING, ERROR)
- Progress tracking and error reporting

## 🔧 Configuration

### Spider Settings

Key settings in `news_scraping/settings.py`:

```python
# Rate limiting
DOWNLOAD_DELAY = 1                    # 1 second delay between requests
CONCURRENT_REQUESTS_PER_DOMAIN = 1   # 1 concurrent request per domain

# Encoding
FEED_EXPORT_ENCODING = "utf-8"       # UTF-8 encoding for Vietnamese text

# Robots.txt
ROBOTSTXT_OBEY = False               # Ignore robots.txt restrictions
```

### Playwright Configuration

For spiders using Playwright (vnexpress, vnexpressV2):

```python
# Timeout settings
PLAYWRIGHT_DEFAULT_NAVIGATION_TIMEOUT = 90000  # 90 seconds

# Page load strategy
PLAYWRIGHT_GOTO_DEFAULT_OPTIONS = {
    "wait_until": "domcontentloaded"
}
```

## 📊 Output Data Format

### NewsScrapingItem Structure

```json
{
  "title": "Article headline",
  "sapo": "Article summary/lead",
  "content": ["Paragraph 1", "Paragraph 2", "..."],
  "author": "Author name",
  "url": "https://example.com/article",
  "source": "vnexpress",
  "category": "Environment",
  "tags": ["pollution", "environment", "..."],
  "published_date": "2024-01-15",
  "scraped_at": "2024-01-15T10:30:00"
}
```

### CommentItem Structure

```json
{
  "nickname": "Commenter name",
  "content": "Comment text",
  "timestamp": "2024-01-15T10:30:00",
  "interactions": 15,
  "replies": [...]
}
```

## 🚨 Important Notes

### Rate Limiting and Ethics

- **Respect Rate Limits**: The default settings include delays to avoid overwhelming servers
- **Monitor Resource Usage**: Keep an eye on bandwidth and server load
- **Follow Terms of Service**: Ensure compliance with each website's terms of service
- **Data Usage**: Use scraped data responsibly and in accordance with applicable laws

### Common Issues and Solutions

#### 1. Playwright Installation Issues
```bash
# If Playwright browsers fail to install
uv run playwright install --force

# For specific browsers only
uv run playwright install chromium
```

#### 2. Permission Errors
```bash
# On Linux/Mac, you might need to install system dependencies
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

#### 3. Memory Issues
```bash
# Reduce concurrent requests if running into memory issues
scrapy crawl vnexpress -s CONCURRENT_REQUESTS=1 -s CONCURRENT_REQUESTS_PER_DOMAIN=1
```

#### 4. Encoding Issues
The project is configured for UTF-8 encoding to handle Vietnamese text properly. If you encounter encoding issues, ensure your terminal supports UTF-8.

### Performance Tips

1. **Use JSON Lines format** for large datasets: `-o articles.jl`
2. **Adjust download delays** based on website responsiveness
3. **Monitor log files** for errors and performance metrics
4. **Use appropriate output formats** (JSON for structured data, CSV for analysis)

## 🔍 Monitoring and Debugging

### Viewing Logs
```bash
# View real-time logs
tail -f news_scraping/logs/scrape_vnexpress_log.txt

# View all logs
ls news_scraping/logs/
```

### Debug Mode
```bash
# Run with verbose debugging
scrapy crawl vnexpress -L DEBUG

# Save debug output to file
scrapy crawl vnexpress -L DEBUG > debug.log 2>&1
```

### Testing Individual Components
```bash
# Test comment API functionality
cd news_scraping/news_scraping
python test.py
```

## 📈 Scaling and Customization

### Adding New Spiders

1. Create a new spider file in `news_scraping/spiders/`
2. Inherit from `scrapy.Spider`
3. Define `name`, `allowed_domains`, and `start_urls`
4. Implement `parse()` method
5. Use `NewsScrapingItem` for consistent data structure

### Customizing Search Keywords

Edit the search keywords in individual spider files:
```python
# In thanhnien.py
SEARCH_KEYWORD = "your custom keyword"

# In tuoitrevn.py
SEARCH_KEYWORDS = "your custom keywords"
```

### Adding Data Processing

Implement custom pipelines in `news_scraping/pipelines.py` for:
- Data cleaning and validation
- Database storage
- Data transformation
- Duplicate detection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped and use the data responsibly.

## 🆘 Support

If you encounter issues:

1. Check the log files in `news_scraping/logs/`
2. Verify your Python and dependency versions
3. Ensure Playwright browsers are properly installed
4. Review the website structure (sites may change their HTML structure)

For technical support, please create an issue in the repository with:
- Error messages
- Log file contents
- System information
- Steps to reproduce the issue
