# 🌍 Hệ Thống Phân Tích và Đ<PERSON>h G<PERSON>á <PERSON>n Đề Ô Nhiễm Tại Việt Nam
### *Dựa Trên Dữ Liệu Tin Tức và Mạng Xã Hội*

<div align="center">

![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)
![Scrapy](https://img.shields.io/badge/Scrapy-2.13+-green.svg)
![GCP](https://img.shields.io/badge/GCP-Cloud%20Run%20%7C%20Vertex%20AI-blue.svg)
![BigQuery](https://img.shields.io/badge/BigQuery-Data%20Warehouse-blue.svg)
![Docker](https://img.shields.io/badge/Docker-Containerized-blue.svg)
![Looker Studio](https://img.shields.io/badge/Looker%20Studio-Visualization-green.svg)

*🏆 Seminar Khoa <PERSON> - Trường Đại <PERSON>, ĐHQG-HCM*

**Giảng viên hướng dẫn:** Th<PERSON><PERSON> Nguyễn Thanh Bình
**Sinh viên thực hiện:** Nguyễn Hồ Nam (22280057), Phạm Tấn Phước (22280069)
**Lớp:** 22KDL

</div>

---

## 🚀 **Tầm Nhìn Dự Án**

Dự án này xây dựng một **hệ thống phân tích dữ liệu end-to-end** kết hợp **web scraping**, **cloud computing**, **AI/ML**, và **trực quan hóa dữ liệu** để tạo ra một nền tảng phân tích thông minh cho báo chí môi trường Việt Nam.

Hệ thống tự động **thu thập**, **xử lý**, **phân tích**, và **làm giàu** dữ liệu tin tức môi trường bằng các mô hình AI tiên tiến được triển khai trên nền tảng đám mây.

---

## 🏗️ **Kiến Trúc Hệ Thống**

<div align="center">

![System Architecture](pics/architecture.png)

*Kiến trúc tổng thể của hệ thống phân tích ô nhiễm môi trường*

</div>

### 📊 **Các Thành Phần Chính**

1. **📰 Data Sources Layer** - Thu thập dữ liệu từ 5 trang báo điện tử lớn
2. **☁️ Cloud Data Platform** - Xử lý và phân tích trên Google Cloud Platform
3. **🤖 AI/ML Layer** - Phân loại và trích xuất thông tin bằng Vertex AI
4. **📈 Consumption System** - Trực quan hóa qua BigQuery và Looker Studio

---

## 🎯 **Khả Năng Cốt Lõi**

### 📰 **Thu Thập Tin Tức Thông Minh**
- **Cào dữ liệu đa nguồn** từ 5 trang báo điện tử hàng đầu Việt Nam
- **Xử lý nội dung động** với Playwright browser automation
- **Trích xuất dữ liệu thời gian thực** với cơ chế chống bot
- **Thu thập metadata toàn diện** bao gồm bình luận và tương tác xã hội

### ☁️ **Pipeline Xử Lý Cloud-Native**
- **Google Cloud Run** microservices cho xử lý dữ liệu có thể mở rộng
- **Google Cloud Storage** làm Data Lake với kiến trúc 3 tầng (Raw/Cleaned/Inferenced)
- **Docker containerization** cho môi trường triển khai nhất quán
- **BigQuery** làm Data Warehouse cho phân tích quy mô lớn

### 🤖 **Phân Tích Được Hỗ Trợ Bởi AI**
- **Fine-tuning mô hình tùy chỉnh** cho phân loại loại ô nhiễm (PhoBERT)
- **Phân tích cảm xúc bình luận** bằng ViSoBERT
- **Google Vertex AI** cho các workflow machine learning nâng cao
- **Trích xuất thực thể tự động** và làm giàu nội dung

---

## 🛠️ **Công Nghệ Sử Dụng**

<div align="center">

| **Danh Mục** | **Công Nghệ** | **Mục Đích** |
|--------------|---------------|--------------|
| **🐍 Core** | Python 3.12+, Scrapy 2.13+, Playwright | Web scraping & automation |
| **☁️ Cloud Platform** | Google Cloud Platform (GCP) | Nền tảng đám mây chính |
| **🗄️ Data Storage** | Cloud Storage, BigQuery | Data Lake & Data Warehouse |
| **🔄 Data Processing** | Cloud Run, Pandas, JSON | Xử lý và biến đổi dữ liệu |
| **🤖 AI/ML** | Vertex AI, PhoBERT, ViSoBERT | Phân tích ngôn ngữ tự nhiên |
| **📊 Visualization** | Looker Studio | Trực quan hóa và dashboard |
| **🚀 Deployment** | Docker, Artifact Registry | Container hóa và triển khai |

</div>

### 🔧 **Chi Tiết Công Nghệ**

- **Web Scraping**: Scrapy 2.13+ với Playwright integration
- **AI Models**:
  - PhoBERT v2 (vinai/phobert-base-v2) cho phân loại ô nhiễm
  - ViSoBERT (5CD-AI/Vietnamese-Sentiment-visobert) cho phân tích cảm xúc
  - Vertex AI Gemini 2.0 Flash Lite cho trích xuất thực thể
- **Data Pipeline**: 3-tier Data Lake (Raw → Cleaned → Inferenced)
- **Containerization**: Docker với Google Artifact Registry
- **Analytics**: BigQuery External Tables với Looker Studio dashboards

---

## 📋 **Yêu Cầu Hệ Thống**

- **Python 3.12+** với package management hiện đại
- **Docker** cho containerization
- **Google Cloud Platform Account** với quyền truy cập:
  - Cloud Storage, Cloud Run, Vertex AI, BigQuery, Artifact Registry
- **uv package manager** (khuyến nghị) hoặc pip

---

## 🚀 **Hướng Dẫn Triển Khai**

### 🔧 **Thiết Lập Môi Trường Local**

```bash
# 1. Clone repository
git clone <repository-url>
cd Scrape_papers

# 2. Cài đặt dependencies (khuyến nghị dùng uv)
uv sync
uv run playwright install

# 3. Hoặc sử dụng pip
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -e .
playwright install
```

### ☁️ **Triển Khai Google Cloud Platform**

```bash
# 1. Thiết lập GCP credentials
gcloud auth login
gcloud config set project YOUR_PROJECT_ID

# 2. Tạo Cloud Storage buckets
gsutil mb gs://bronze-bucket-namseminar      # Raw data
gsutil mb gs://silver-bucket-namseminar      # Cleaned data
gsutil mb gs://seminar-inferenced-data-bucket # AI processed data

# 3. Deploy Cloud Run services
cd cloudRun/clean_data
gcloud run deploy clean-data-service --source .

cd ../merge_data
gcloud run deploy merge-data-service --source .

# 4. Deploy AI models to Cloud Run
cd ../../modelFintuning
# Build và push Docker images cho PhoBERT và ViSoBERT models
docker build -t pollution-classification-api .
docker push gcr.io/YOUR_PROJECT_ID/pollution-classification-api

gcloud run deploy pollution-classifier \
  --image gcr.io/YOUR_PROJECT_ID/pollution-classification-api
```

### ⚡ **Chạy Thử Nghiệm**

```bash
# Test thu thập dữ liệu
cd news_scraping
scrapy crawl vnexpress -o sample_data.json

# Test xử lý dữ liệu với Cloud Run
curl -X POST https://clean-data-service-url/clean \
  -H "Content-Type: application/json" \
  -d '{"input_bucket": "bronze-bucket-namseminar"}'

# Test AI inference với Vertex AI
cd ../bedrocks-apis(ECS)
python gcp.py
```

---

## 🎮 **Hướng Dẫn Sử Dụng**

### 📰 **Thu Thập Dữ Liệu Tin Tức**

<details>
<summary><b>🔍 Chạy Các Spider Riêng Lẻ</b></summary>

```bash
cd news_scraping

# VnExpress - Trang báo hàng đầu Việt Nam
scrapy crawl vnexpress -o vnexpress_articles.json
scrapy crawl vnexpressV2 -o vnexpress_v2_articles.json  # Phiên bản tối ưu

# Các trang báo lớn khác
scrapy crawl thanhnien -o thanhnien_articles.json      # Thanh Niên
scrapy crawl tuoitrevn -o tuoitre_articles.json        # Tuổi Trẻ
scrapy crawl vietnamnet -o vietnamnet_articles.json    # VietnamNet
scrapy crawl dantri -o dantri_articles.json            # Dân Trí
```

</details>

<details>
<summary><b>📊 Định Dạng Đầu Ra Đa Dạng</b></summary>

```bash
# Hỗ trợ nhiều định dạng output
scrapy crawl vnexpress -o articles.json    # JSON (có cấu trúc)
scrapy crawl vnexpress -o articles.csv     # CSV (sẵn sàng phân tích)
scrapy crawl vnexpress -o articles.jl      # JSON Lines (streaming)

# Cấu hình tùy chỉnh
scrapy crawl vnexpress -s DOWNLOAD_DELAY=2 -o articles.json
scrapy crawl vnexpress -s CONCURRENT_REQUESTS_PER_DOMAIN=1 -o articles.json
scrapy crawl vnexpress -L DEBUG -o debug_articles.json
```

</details>

### ☁️ **Pipeline Xử Lý Dữ Liệu**

<details>
<summary><b>🔄 Quy Trình Xử Lý Dữ Liệu</b></summary>

```bash
# 1. Làm sạch dữ liệu thô bằng GCP Cloud Run
curl -X POST https://clean-data-service-url/clean \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "bronze-bucket-namseminar",
    "input_prefix": "vnexpress/2024-01-15/",
    "output_bucket": "silver-bucket-namseminar"
  }'

# 2. Gộp dữ liệu từ nhiều nguồn
curl -X POST https://merge-data-service-url/merge \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "silver-bucket-namseminar",
    "output_bucket": "seminar-inferenced-data-bucket"
  }'
```

</details>

### 🤖 **Phân Tích AI & Fine-tuning Mô Hình**

<details>
<summary><b>🧠 Vertex AI Integration</b></summary>

```bash
cd bedrocks-apis(ECS)

# Xử lý dữ liệu với Google Vertex AI
python gcp.py

# Chạy pipeline inference đầy đủ
python b.py  # Batch processing với Vertex AI
```

</details>

<details>
<summary><b>🎯 Fine-tuning Mô Hình Tùy Chỉnh</b></summary>

```bash
# Fine-tune mô hình phân loại ô nhiễm
jupyter notebook modelFintuning/polutionTypeFituning.ipynb

# Kết quả đạt được:
# - Accuracy: 94.54%
# - Macro F1-score: 91.17%
# - Weighted F1-score: 94.61%
```

</details>

### 📊 **Trực Quan Hóa Dữ Liệu**

<details>
<summary><b>📈 BigQuery & Looker Studio</b></summary>

```sql
-- Import dữ liệu vào BigQuery
CREATE OR REPLACE EXTERNAL TABLE `project.dataset.news_data`
OPTIONS (
  format = 'NEWLINE_DELIMITED_JSON',
  uris = ['gs://seminar-inferenced-data-bucket/merged/*.json']
);

-- Tạo view cho dashboard
CREATE OR REPLACE VIEW `project.dataset.pollution_analysis` AS
SELECT
  source,
  pollution_type,
  location,
  sentiment_score,
  published_date
FROM `project.dataset.news_data`
WHERE pollution_type IS NOT NULL;
```

</details>

---

## 🏗️ **Project Architecture**

### 📁 **Directory Structure**

```
🌍 Scrape_papers/                    # Root project directory
├── 📄 main.py                       # Entry point for standalone execution
├── 🐍 vnexpress.py                  # Standalone VnExpress spider
├── 📋 pyproject.toml                # Modern Python project configuration
├── 🔒 uv.lock                       # Dependency lock file
├── 📖 README.md                     # Project documentation
│
├── 🕷️ news_scraping/                # Core Scrapy framework
│   ├── ⚙️ scrapy.cfg                # Scrapy project configuration
│   └── 📦 news_scraping/
│       ├── 🏗️ items.py              # Data models & schemas
│       ├── 🔧 middlewares.py        # Custom request/response processing
│       ├── 🔄 pipelines.py          # Data processing & validation
│       ├── ⚙️ settings.py           # Scrapy configuration settings
│       ├── 🧪 test.py               # API testing utilities
│       ├── 📊 logs/                 # Spider execution logs
│       └── 🕸️ spiders/              # News source implementations
│           ├── 📰 vnexpress.py      # VnExpress v1 (Playwright)
│           ├── 📰 vnexpressV2.py    # VnExpress v2 (Optimized)
│           ├── 📰 thanhnien.py      # Thanh Niên spider
│           ├── 📰 tuoitrevn.py      # Tuổi Trẻ spider
│           ├── 📰 vietnamnet.py     # VietnamNet spider
│           └── 📰 dantri.py         # Dân Trí spider
│
├── ☁️ cloudRun/                     # Google Cloud Run microservices
│   ├── 🧹 clean_data/              # Data cleaning service
│   │   ├── 🐳 Dockerfile           # Container configuration
│   │   ├── 🐍 main.py              # Flask-based cleaning API
│   │   └── 📋 requirements.txt     # Python dependencies
│   └── 🔗 merge_data/              # Data merging service
│       ├── 🐳 Dockerfile           # Container configuration
│       ├── 🐍 main.py              # Flask-based merging API
│       └── 📋 requirements.txt     # Python dependencies
│
├── 🤖 bedrocks-apis(ECS)/          # AWS Bedrock AI integration
│   ├── 🧪 testBedrock.py           # Claude 3 Haiku testing
│   ├── ⚡ b.py                     # Batch processing pipeline
│   ├── ☁️ gcp.py                   # GCP Vertex AI integration
│   └── 🔑 deployment.pem          # AWS deployment credentials
│
├── 🔄 airflow/                     # Apache Airflow orchestration
│   ├── 🐳 docker-compose.yml       # Multi-service deployment
│   ├── 🐳 Dockerfile               # Custom Airflow image
│   ├── 🚀 entrypoint.sh            # Container initialization
│   └── 📊 dags/                    # Workflow definitions
│       └── 📅 wed.py               # Sample DAG implementation
│
└── 🧠 modelFintuning/              # AI/ML model development
    └── 📓 polutionTypeFituning.ipynb # Custom classification model
```

---

## 🔬 **How It Works**

### 🎯 **Multi-Source Data Acquisition Strategy**

<div align="center">

| **News Source** | **Strategy** | **Technology** | **Coverage** |
|-----------------|--------------|----------------|--------------|
| **🏆 VnExpress** | Dedicated topic page scraping | Playwright + Scrapy | Comprehensive |
| **📰 Thanh Niên** | Search API with keywords | HTTP requests | Targeted |
| **🌟 Tuổi Trẻ** | Timeline search API | REST API integration | Time-based |
| **🌐 VietnamNet** | Encoded keyword search | Advanced parsing | Deep search |
| **📊 Dân Trí** | Search interface automation | Dynamic content | Real-time |

</div>

### 🔄 **End-to-End Data Pipeline**

```mermaid
sequenceDiagram
    participant NS as News Sources
    participant SP as Scrapy Spiders
    participant CR as Cloud Run
    participant BR as AWS Bedrock
    participant VA as Vertex AI
    participant ST as Storage

    NS->>SP: 1. Article Discovery
    SP->>SP: 2. Content Extraction
    SP->>CR: 3. Raw Data Processing
    CR->>BR: 4. AI Content Analysis
    BR->>VA: 5. Model Fine-tuning
    VA->>ST: 6. Enriched Data Storage
```

### 🛡️ **Advanced Anti-Bot Protection**

- **🕰️ Intelligent Rate Limiting**: Dynamic delays based on server response
- **🎭 User Agent Rotation**: Multiple browser fingerprints
- **🌐 Playwright Integration**: Full browser automation for JavaScript-heavy sites
- **⚡ Resource Optimization**: Selective content loading (block images, ads, scripts)
- **🔄 Request Retry Logic**: Exponential backoff with jitter
- **📊 Real-time Monitoring**: Performance metrics and error tracking

### 📈 **Monitoring & Observability**

- **📋 Structured Logging**: JSON-formatted logs with correlation IDs
- **📊 Performance Metrics**: Request latency, success rates, data quality scores
- **🚨 Error Tracking**: Automated failure detection and alerting
- **📈 Progress Dashboards**: Real-time scraping progress visualization

---

## ⚙️ **Configuration & Deployment**

### 🕷️ **Scrapy Configuration**

<details>
<summary><b>🔧 Core Spider Settings</b></summary>

```python
# news_scraping/settings.py

# Performance & Rate Limiting
DOWNLOAD_DELAY = 1                    # Respectful 1-second delay
CONCURRENT_REQUESTS_PER_DOMAIN = 1   # Single concurrent request per domain
RANDOMIZE_DOWNLOAD_DELAY = 0.5       # Random delay variation (0.5-1.5x)

# Vietnamese Language Support
FEED_EXPORT_ENCODING = "utf-8"       # UTF-8 for Vietnamese characters
DEFAULT_REQUEST_HEADERS = {
    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
}

# Anti-Bot Measures
ROBOTSTXT_OBEY = False               # Bypass robots.txt for research
COOKIES_ENABLED = True               # Maintain session state
TELNETCONSOLE_ENABLED = False        # Disable telnet for security
```

</details>

<details>
<summary><b>🎭 Playwright Browser Automation</b></summary>

```python
# Advanced Playwright Configuration
PLAYWRIGHT_DEFAULT_NAVIGATION_TIMEOUT = 90000  # 90-second timeout
PLAYWRIGHT_GOTO_DEFAULT_OPTIONS = {
    "wait_until": "domcontentloaded",           # Fast page loading
    "timeout": 60000                            # 60-second page timeout
}

# Resource Optimization
PLAYWRIGHT_LAUNCH_OPTIONS = {
    "headless": True,                           # Headless browser mode
    "args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security"
    ]
}
```

</details>

### ☁️ **Cloud Infrastructure Configuration**

<details>
<summary><b>🐳 Docker & Orchestration</b></summary>

```yaml
# airflow/docker-compose.yml
version: '3'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data

  webserver:
    build: .
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
    ports:
      - "8080:8080"
    command: webserver

  scheduler:
    build: .
    command: scheduler
    depends_on:
      - webserver
      - postgres
```

</details>

<details>
<summary><b>🤖 AI Model Configuration</b></summary>

```python
# AWS Bedrock Configuration
AWS_REGION = "us-east-1"
BEDROCK_MODEL_ID = "anthropic.claude-3-haiku-20240307-v1:0"

# GCP Vertex AI Configuration
GCP_PROJECT_ID = "wired-archery-481312-h3"
GCP_REGION = "us-central1"
SOURCE_BUCKET = "silver-bucket-namseminar"
DEST_BUCKET = "seminar-inferenced-data-bucket"
```

</details>

---

## 📊 **Data Schema & Output Formats**

### 📰 **NewsScrapingItem Schema**

```json
{
  "title": "Ô nhiễm không khí tại Hà Nội đạt mức nguy hiểm",
  "sapo": "Chỉ số AQI tại nhiều khu vực Hà Nội vượt ngưỡng 200...",
  "content": [
    "Theo số liệu từ Trung tâm Quan trắc môi trường...",
    "Nguyên nhân chính được xác định là do..."
  ],
  "author": "Nguyễn Văn A",
  "url": "https://vnexpress.net/o-nhiem-khong-khi-ha-noi-4567890.html",
  "source": "vnexpress",
  "category": "Môi trường",
  "tags": ["ô nhiễm", "không khí", "Hà Nội", "AQI"],
  "published_date": "2024-01-15T08:30:00+07:00",
  "scraped_at": "2024-01-15T10:30:00+07:00",
  "comments": [...] // CommentItem array
}
```

### 💬 **CommentItem Schema**

```json
{
  "nickname": "user123",
  "content": "Tình trạng ô nhiễm ngày càng nghiêm trọng...",
  "timestamp": "2024-01-15T09:15:00+07:00",
  "interactions": 25,
  "replies": [
    {
      "nickname": "expert_env",
      "content": "Cần có biện pháp cấp bách...",
      "timestamp": "2024-01-15T09:45:00+07:00",
      "interactions": 12
    }
  ]
}
```

---

## 🚨 **Important Considerations**

### 🤝 **Ethical Guidelines & Best Practices**

<div align="center">

| **Principle** | **Implementation** | **Monitoring** |
|---------------|-------------------|----------------|
| **🕰️ Rate Limiting** | 1-second delays, respectful crawling | Real-time request monitoring |
| **📊 Resource Management** | Bandwidth throttling, memory optimization | Performance dashboards |
| **⚖️ Legal Compliance** | Terms of service adherence | Compliance auditing |
| **🔒 Data Privacy** | Anonymization, secure storage | Privacy impact assessments |

</div>

### 🛠️ **Troubleshooting Guide**

<details>
<summary><b>🎭 Playwright Installation Issues</b></summary>

```bash
# Force reinstall Playwright browsers
uv run playwright install --force

# Install specific browsers only
uv run playwright install chromium firefox

# System dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 \
    libxcomposite1 libxdamage1 libxrandr2 libgbm1 \
    libxss1 libasound2 libatspi2.0-0 libgtk-3-0
```

</details>

<details>
<summary><b>💾 Memory & Performance Optimization</b></summary>

```bash
# Reduce memory usage
scrapy crawl vnexpress \
  -s CONCURRENT_REQUESTS=1 \
  -s CONCURRENT_REQUESTS_PER_DOMAIN=1 \
  -s DOWNLOAD_DELAY=2

# Use streaming output for large datasets
scrapy crawl vnexpress -o articles.jl  # JSON Lines format

# Enable compression
scrapy crawl vnexpress -o articles.json.gz
```

</details>

<details>
<summary><b>🌐 Vietnamese Text Encoding</b></summary>

```bash
# Ensure UTF-8 support
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# Verify encoding in output
file -i articles.json  # Should show charset=utf-8
```

</details>

---

## 📈 **Monitoring & Analytics**

### 📊 **Real-time Monitoring Dashboard**

<details>
<summary><b>🔍 Log Analysis & Debugging</b></summary>

```bash
# Real-time log monitoring
tail -f news_scraping/logs/scrape_vnexpress_log.txt

# Multi-spider log monitoring
tail -f news_scraping/logs/*.txt

# Advanced log analysis
grep "ERROR" news_scraping/logs/*.txt | wc -l  # Count errors
grep "scraped" news_scraping/logs/*.txt | tail -10  # Recent scrapes

# Debug mode with detailed output
scrapy crawl vnexpress -L DEBUG -o debug_output.json 2>&1 | tee debug.log
```

</details>

<details>
<summary><b>🧪 Component Testing</b></summary>

```bash
# Test individual components
cd news_scraping/news_scraping

# Test comment API functionality
python test.py

# Test AWS Bedrock integration
cd ../../bedrocks-apis(ECS)
python testBedrock.py

# Test GCP Vertex AI pipeline
python gcp.py

# Test data processing services
curl -X POST http://localhost:8080/clean -d '{"test": "data"}'
```

</details>

### 📈 **Performance Metrics**

- **📊 Scraping Rate**: Articles per minute across all sources
- **🎯 Success Rate**: Percentage of successful article extractions
- **⚡ Response Time**: Average time per article processing
- **🔄 Data Quality**: Completeness and accuracy scores
- **☁️ Cloud Costs**: AWS Bedrock and GCP usage monitoring

---

## 🚀 **Scaling & Advanced Features**

### 🔧 **Extensibility Framework**

<details>
<summary><b>🕷️ Adding New News Sources</b></summary>

```python
# Create new spider: news_scraping/spiders/new_source.py
import scrapy
from news_scraping.items import NewsScrapingItem

class NewSourceSpider(scrapy.Spider):
    name = "new_source"
    allowed_domains = ["newssite.com"]
    start_urls = ["https://newssite.com/environment"]

    custom_settings = {
        'LOG_FILE': 'logs/scrape_new_source_log.txt',
        'DOWNLOAD_DELAY': 1,
    }

    def parse(self, response):
        # Extract article URLs
        article_urls = response.css('a.article-link::attr(href)').getall()

        for url in article_urls:
            yield response.follow(url, self.parse_article)

    def parse_article(self, response):
        item = NewsScrapingItem()
        item['title'] = response.css('h1.title::text').get()
        item['content'] = response.css('div.content p::text').getall()
        item['source'] = self.name
        # ... additional fields
        yield item
```

</details>

<details>
<summary><b>🎯 Custom Search & Filtering</b></summary>

```python
# Customize search parameters
ENVIRONMENTAL_KEYWORDS = [
    "ô nhiễm môi trường",
    "biến đổi khí hậu",
    "ô nhiễm không khí",
    "ô nhiễm nước",
    "rác thải nhựa"
]

# Advanced filtering pipeline
class EnvironmentalFilterPipeline:
    def process_item(self, item, spider):
        content = item.get('content', '')
        if any(keyword in content.lower() for keyword in ENVIRONMENTAL_KEYWORDS):
            return item
        else:
            raise DropItem(f"Non-environmental article: {item.get('title')}")
```

</details>

### ☁️ **Cloud Scaling Strategies**

- **🔄 Auto-scaling**: Cloud Run instances based on request volume
- **📊 Load Balancing**: Distribute scraping across multiple regions
- **💾 Data Partitioning**: Time-based and source-based data organization
- **🚀 Serverless Functions**: Event-driven processing with Cloud Functions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped and use the data responsibly.

## 🆘 Support

If you encounter issues:

1. Check the log files in `news_scraping/logs/`
2. Verify your Python and dependency versions
3. Ensure Playwright browsers are properly installed
4. Review the website structure (sites may change their HTML structure)

For technical support, please create an issue in the repository with:
- Error messages
- Log file contents
- System information
- Steps to reproduce the issue
