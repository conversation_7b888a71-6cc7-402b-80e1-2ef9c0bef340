# 🌍 Intelligent Environmental News Analytics Platform
### *A Cloud-Native AI-Powered Data Pipeline for Vietnamese Environmental Journalism*

<div align="center">

![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)
![Scrapy](https://img.shields.io/badge/Scrapy-2.13+-green.svg)
![AWS](https://img.shields.io/badge/AWS-Bedrock%20%7C%20ECS-orange.svg)
![GCP](https://img.shields.io/badge/GCP-Cloud%20Run%20%7C%20Vertex%20AI-blue.svg)
![Apache Airflow](https://img.shields.io/badge/Apache-Airflow-red.svg)
![Docker](https://img.shields.io/badge/Docker-Containerized-blue.svg)

*🏆 Advanced Seminar Project - Data Engineering & AI Analytics*

</div>

---

## 🚀 **Project Vision**

This project represents a **comprehensive end-to-end data engineering solution** that combines **web scraping**, **cloud computing**, **AI/ML**, and **workflow orchestration** to create an intelligent analytics platform for Vietnamese environmental journalism.

The system automatically **collects**, **processes**, **analyzes**, and **enriches** environmental news data using state-of-the-art AI models deployed across multiple cloud platforms.

---

## 🏗️ **System Architecture**

<div align="center">

```mermaid
graph TB
    A[Vietnamese News Sources] --> B[Scrapy + Playwright Spiders]
    B --> C[Raw Data Storage]
    C --> D[Apache Airflow Orchestration]
    D --> E[GCP Cloud Run Data Processing]
    E --> F[AWS Bedrock AI Analysis]
    F --> G[Vertex AI Model Fine-tuning]
    G --> H[Enriched Analytics Dashboard]

    subgraph "Data Sources"
        A1[VnExpress]
        A2[Thanh Niên]
        A3[Tuổi Trẻ]
        A4[VietnamNet]
        A5[Dân Trí]
    end

    subgraph "Cloud Infrastructure"
        E1[Data Cleaning Service]
        E2[Data Merging Service]
        F1[Claude 3 Haiku]
        F2[AWS ECS Deployment]
        G1[Custom Pollution Classification]
    end
```

</div>

---

## 🎯 **Core Capabilities**

### 📰 **Intelligent News Aggregation**
- **Multi-source scraping** from 5 major Vietnamese news platforms
- **Dynamic content handling** with Playwright browser automation
- **Real-time data extraction** with anti-bot protection mechanisms
- **Comprehensive metadata collection** including comments and social interactions

### ☁️ **Cloud-Native Processing Pipeline**
- **Google Cloud Run** microservices for scalable data processing
- **AWS Bedrock** integration for advanced AI text analysis
- **Apache Airflow** for robust workflow orchestration
- **Docker containerization** for consistent deployment environments

### 🤖 **AI-Powered Analytics**
- **Custom model fine-tuning** for pollution type classification
- **AWS Bedrock Claude 3** integration for content analysis
- **Google Vertex AI** for advanced machine learning workflows
- **Automated content enrichment** and sentiment analysis

---

## 🛠️ **Technology Stack**

<div align="center">

| **Category** | **Technologies** | **Purpose** |
|--------------|------------------|-------------|
| **🐍 Core** | Python 3.12+, Scrapy 2.13+, Playwright | Web scraping & automation |
| **☁️ Cloud Platforms** | AWS (Bedrock, ECS), GCP (Cloud Run, Vertex AI) | Scalable AI & compute services |
| **🔄 Orchestration** | Apache Airflow, Docker Compose | Workflow management |
| **🤖 AI/ML** | AWS Bedrock Claude 3, Google Vertex AI | Natural language processing |
| **📊 Data Processing** | Pandas, JSON processing, Cloud Storage | Data transformation & storage |
| **🚀 Deployment** | Docker, Cloud Run, ECS | Containerized microservices |

</div>

---

## 📋 **Prerequisites**

- **Python 3.12+** with modern package management
- **Docker & Docker Compose** for containerization
- **Cloud Platform Access**: AWS (Bedrock) & GCP (Cloud Run, Vertex AI)
- **uv package manager** (recommended) or pip

---

## 🚀 **Quick Start Guide**

### 🔧 **Local Development Setup**

```bash
# 1. Clone the repository
git clone <repository-url>
cd Scrape_papers

# 2. Install dependencies (using uv - recommended)
uv sync
uv run playwright install

# 3. Alternative: Using pip
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -e .
playwright install
```

### 🐳 **Docker Deployment**

```bash
# 1. Start Apache Airflow with PostgreSQL
cd airflow
docker-compose up -d

# 2. Deploy Cloud Run services
cd ../cloudRun/clean_data
docker build -t clean-data-service .
gcloud run deploy clean-data --image clean-data-service

cd ../merge_data
docker build -t merge-data-service .
gcloud run deploy merge-data --image merge-data-service

# 3. Configure AWS Bedrock credentials
cd ../../bedrocks-apis(ECS)
# Add your AWS credentials and deploy to ECS
```

### ⚡ **Quick Test Run**

```bash
# Test individual news scrapers
cd news_scraping
scrapy crawl vnexpress -o sample_data.json

# Test AI processing pipeline
cd ../bedrocks-apis(ECS)
python testBedrock.py

# Test GCP Vertex AI integration
python gcp.py
```

---

## 🎮 **Usage Examples**

### 📰 **News Scraping Operations**

<details>
<summary><b>🔍 Individual Spider Execution</b></summary>

```bash
cd news_scraping

# VnExpress - Vietnam's leading news source
scrapy crawl vnexpress -o vnexpress_articles.json
scrapy crawl vnexpressV2 -o vnexpress_v2_articles.json  # Optimized version

# Major Vietnamese newspapers
scrapy crawl thanhnien -o thanhnien_articles.json      # Thanh Niên
scrapy crawl tuoitrevn -o tuoitre_articles.json        # Tuổi Trẻ
scrapy crawl vietnamnet -o vietnamnet_articles.json    # VietnamNet
scrapy crawl dantri -o dantri_articles.json            # Dân Trí
```

</details>

<details>
<summary><b>📊 Advanced Output Formats</b></summary>

```bash
# Multiple output formats supported
scrapy crawl vnexpress -o articles.json    # JSON (structured)
scrapy crawl vnexpress -o articles.csv     # CSV (analysis-ready)
scrapy crawl vnexpress -o articles.xml     # XML (enterprise)
scrapy crawl vnexpress -o articles.jl      # JSON Lines (streaming)

# Custom configurations
scrapy crawl vnexpress -s DOWNLOAD_DELAY=2 -o articles.json
scrapy crawl vnexpress -s CONCURRENT_REQUESTS_PER_DOMAIN=2 -o articles.json
scrapy crawl vnexpress -L DEBUG -o debug_articles.json
```

</details>

### ☁️ **Cloud Processing Pipeline**

<details>
<summary><b>🔄 Data Processing Workflow</b></summary>

```bash
# 1. Clean raw scraped data using GCP Cloud Run
curl -X POST https://clean-data-service-url/clean \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "raw-news-data",
    "input_prefix": "scraped/",
    "output_bucket": "processed-news-data",
    "output_prefix": "cleaned/"
  }'

# 2. Merge multiple data sources
curl -X POST https://merge-data-service-url/merge \
  -H "Content-Type: application/json" \
  -d '{
    "input_bucket": "processed-news-data",
    "output_bucket": "merged-news-data"
  }'
```

</details>

### 🤖 **AI Analysis & Model Fine-tuning**

<details>
<summary><b>🧠 AWS Bedrock Integration</b></summary>

```bash
cd bedrocks-apis(ECS)

# Test AWS Bedrock Claude 3 Haiku connection
python testBedrock.py

# Run full AI analysis pipeline
python b.py  # Batch processing with Bedrock

# Deploy to AWS ECS for production
# Configure deployment.pem and push to ECS
```

</details>

<details>
<summary><b>🎯 Google Vertex AI Processing</b></summary>

```bash
# Process data with Google Vertex AI
python gcp.py

# Custom model fine-tuning for pollution classification
jupyter notebook modelFintuning/polutionTypeFituning.ipynb
```

</details>

---

## 🏗️ **Project Architecture**

### 📁 **Directory Structure**

```
🌍 Scrape_papers/                    # Root project directory
├── 📄 main.py                       # Entry point for standalone execution
├── 🐍 vnexpress.py                  # Standalone VnExpress spider
├── 📋 pyproject.toml                # Modern Python project configuration
├── 🔒 uv.lock                       # Dependency lock file
├── 📖 README.md                     # Project documentation
│
├── 🕷️ news_scraping/                # Core Scrapy framework
│   ├── ⚙️ scrapy.cfg                # Scrapy project configuration
│   └── 📦 news_scraping/
│       ├── 🏗️ items.py              # Data models & schemas
│       ├── 🔧 middlewares.py        # Custom request/response processing
│       ├── 🔄 pipelines.py          # Data processing & validation
│       ├── ⚙️ settings.py           # Scrapy configuration settings
│       ├── 🧪 test.py               # API testing utilities
│       ├── 📊 logs/                 # Spider execution logs
│       └── 🕸️ spiders/              # News source implementations
│           ├── 📰 vnexpress.py      # VnExpress v1 (Playwright)
│           ├── 📰 vnexpressV2.py    # VnExpress v2 (Optimized)
│           ├── 📰 thanhnien.py      # Thanh Niên spider
│           ├── 📰 tuoitrevn.py      # Tuổi Trẻ spider
│           ├── 📰 vietnamnet.py     # VietnamNet spider
│           └── 📰 dantri.py         # Dân Trí spider
│
├── ☁️ cloudRun/                     # Google Cloud Run microservices
│   ├── 🧹 clean_data/              # Data cleaning service
│   │   ├── 🐳 Dockerfile           # Container configuration
│   │   ├── 🐍 main.py              # Flask-based cleaning API
│   │   └── 📋 requirements.txt     # Python dependencies
│   └── 🔗 merge_data/              # Data merging service
│       ├── 🐳 Dockerfile           # Container configuration
│       ├── 🐍 main.py              # Flask-based merging API
│       └── 📋 requirements.txt     # Python dependencies
│
├── 🤖 bedrocks-apis(ECS)/          # AWS Bedrock AI integration
│   ├── 🧪 testBedrock.py           # Claude 3 Haiku testing
│   ├── ⚡ b.py                     # Batch processing pipeline
│   ├── ☁️ gcp.py                   # GCP Vertex AI integration
│   └── 🔑 deployment.pem          # AWS deployment credentials
│
├── 🔄 airflow/                     # Apache Airflow orchestration
│   ├── 🐳 docker-compose.yml       # Multi-service deployment
│   ├── 🐳 Dockerfile               # Custom Airflow image
│   ├── 🚀 entrypoint.sh            # Container initialization
│   └── 📊 dags/                    # Workflow definitions
│       └── 📅 wed.py               # Sample DAG implementation
│
└── 🧠 modelFintuning/              # AI/ML model development
    └── 📓 polutionTypeFituning.ipynb # Custom classification model
```

---

## 🔬 **How It Works**

### 🎯 **Multi-Source Data Acquisition Strategy**

<div align="center">

| **News Source** | **Strategy** | **Technology** | **Coverage** |
|-----------------|--------------|----------------|--------------|
| **🏆 VnExpress** | Dedicated topic page scraping | Playwright + Scrapy | Comprehensive |
| **📰 Thanh Niên** | Search API with keywords | HTTP requests | Targeted |
| **🌟 Tuổi Trẻ** | Timeline search API | REST API integration | Time-based |
| **🌐 VietnamNet** | Encoded keyword search | Advanced parsing | Deep search |
| **📊 Dân Trí** | Search interface automation | Dynamic content | Real-time |

</div>

### 🔄 **End-to-End Data Pipeline**

```mermaid
sequenceDiagram
    participant NS as News Sources
    participant SP as Scrapy Spiders
    participant CR as Cloud Run
    participant BR as AWS Bedrock
    participant VA as Vertex AI
    participant ST as Storage

    NS->>SP: 1. Article Discovery
    SP->>SP: 2. Content Extraction
    SP->>CR: 3. Raw Data Processing
    CR->>BR: 4. AI Content Analysis
    BR->>VA: 5. Model Fine-tuning
    VA->>ST: 6. Enriched Data Storage
```

### 🛡️ **Advanced Anti-Bot Protection**

- **🕰️ Intelligent Rate Limiting**: Dynamic delays based on server response
- **🎭 User Agent Rotation**: Multiple browser fingerprints
- **🌐 Playwright Integration**: Full browser automation for JavaScript-heavy sites
- **⚡ Resource Optimization**: Selective content loading (block images, ads, scripts)
- **🔄 Request Retry Logic**: Exponential backoff with jitter
- **📊 Real-time Monitoring**: Performance metrics and error tracking

### 📈 **Monitoring & Observability**

- **📋 Structured Logging**: JSON-formatted logs with correlation IDs
- **📊 Performance Metrics**: Request latency, success rates, data quality scores
- **🚨 Error Tracking**: Automated failure detection and alerting
- **📈 Progress Dashboards**: Real-time scraping progress visualization

---

## ⚙️ **Configuration & Deployment**

### 🕷️ **Scrapy Configuration**

<details>
<summary><b>🔧 Core Spider Settings</b></summary>

```python
# news_scraping/settings.py

# Performance & Rate Limiting
DOWNLOAD_DELAY = 1                    # Respectful 1-second delay
CONCURRENT_REQUESTS_PER_DOMAIN = 1   # Single concurrent request per domain
RANDOMIZE_DOWNLOAD_DELAY = 0.5       # Random delay variation (0.5-1.5x)

# Vietnamese Language Support
FEED_EXPORT_ENCODING = "utf-8"       # UTF-8 for Vietnamese characters
DEFAULT_REQUEST_HEADERS = {
    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
}

# Anti-Bot Measures
ROBOTSTXT_OBEY = False               # Bypass robots.txt for research
COOKIES_ENABLED = True               # Maintain session state
TELNETCONSOLE_ENABLED = False        # Disable telnet for security
```

</details>

<details>
<summary><b>🎭 Playwright Browser Automation</b></summary>

```python
# Advanced Playwright Configuration
PLAYWRIGHT_DEFAULT_NAVIGATION_TIMEOUT = 90000  # 90-second timeout
PLAYWRIGHT_GOTO_DEFAULT_OPTIONS = {
    "wait_until": "domcontentloaded",           # Fast page loading
    "timeout": 60000                            # 60-second page timeout
}

# Resource Optimization
PLAYWRIGHT_LAUNCH_OPTIONS = {
    "headless": True,                           # Headless browser mode
    "args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security"
    ]
}
```

</details>

### ☁️ **Cloud Infrastructure Configuration**

<details>
<summary><b>🐳 Docker & Orchestration</b></summary>

```yaml
# airflow/docker-compose.yml
version: '3'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data

  webserver:
    build: .
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
    ports:
      - "8080:8080"
    command: webserver

  scheduler:
    build: .
    command: scheduler
    depends_on:
      - webserver
      - postgres
```

</details>

<details>
<summary><b>🤖 AI Model Configuration</b></summary>

```python
# AWS Bedrock Configuration
AWS_REGION = "us-east-1"
BEDROCK_MODEL_ID = "anthropic.claude-3-haiku-20240307-v1:0"

# GCP Vertex AI Configuration
GCP_PROJECT_ID = "wired-archery-481312-h3"
GCP_REGION = "us-central1"
SOURCE_BUCKET = "silver-bucket-namseminar"
DEST_BUCKET = "seminar-inferenced-data-bucket"
```

</details>

---

## 📊 **Data Schema & Output Formats**

### 📰 **NewsScrapingItem Schema**

```json
{
  "title": "Ô nhiễm không khí tại Hà Nội đạt mức nguy hiểm",
  "sapo": "Chỉ số AQI tại nhiều khu vực Hà Nội vượt ngưỡng 200...",
  "content": [
    "Theo số liệu từ Trung tâm Quan trắc môi trường...",
    "Nguyên nhân chính được xác định là do..."
  ],
  "author": "Nguyễn Văn A",
  "url": "https://vnexpress.net/o-nhiem-khong-khi-ha-noi-4567890.html",
  "source": "vnexpress",
  "category": "Môi trường",
  "tags": ["ô nhiễm", "không khí", "Hà Nội", "AQI"],
  "published_date": "2024-01-15T08:30:00+07:00",
  "scraped_at": "2024-01-15T10:30:00+07:00",
  "comments": [...] // CommentItem array
}
```

### 💬 **CommentItem Schema**

```json
{
  "nickname": "user123",
  "content": "Tình trạng ô nhiễm ngày càng nghiêm trọng...",
  "timestamp": "2024-01-15T09:15:00+07:00",
  "interactions": 25,
  "replies": [
    {
      "nickname": "expert_env",
      "content": "Cần có biện pháp cấp bách...",
      "timestamp": "2024-01-15T09:45:00+07:00",
      "interactions": 12
    }
  ]
}
```

---

## 🚨 **Important Considerations**

### 🤝 **Ethical Guidelines & Best Practices**

<div align="center">

| **Principle** | **Implementation** | **Monitoring** |
|---------------|-------------------|----------------|
| **🕰️ Rate Limiting** | 1-second delays, respectful crawling | Real-time request monitoring |
| **📊 Resource Management** | Bandwidth throttling, memory optimization | Performance dashboards |
| **⚖️ Legal Compliance** | Terms of service adherence | Compliance auditing |
| **🔒 Data Privacy** | Anonymization, secure storage | Privacy impact assessments |

</div>

### 🛠️ **Troubleshooting Guide**

<details>
<summary><b>🎭 Playwright Installation Issues</b></summary>

```bash
# Force reinstall Playwright browsers
uv run playwright install --force

# Install specific browsers only
uv run playwright install chromium firefox

# System dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 \
    libxcomposite1 libxdamage1 libxrandr2 libgbm1 \
    libxss1 libasound2 libatspi2.0-0 libgtk-3-0
```

</details>

<details>
<summary><b>💾 Memory & Performance Optimization</b></summary>

```bash
# Reduce memory usage
scrapy crawl vnexpress \
  -s CONCURRENT_REQUESTS=1 \
  -s CONCURRENT_REQUESTS_PER_DOMAIN=1 \
  -s DOWNLOAD_DELAY=2

# Use streaming output for large datasets
scrapy crawl vnexpress -o articles.jl  # JSON Lines format

# Enable compression
scrapy crawl vnexpress -o articles.json.gz
```

</details>

<details>
<summary><b>🌐 Vietnamese Text Encoding</b></summary>

```bash
# Ensure UTF-8 support
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# Verify encoding in output
file -i articles.json  # Should show charset=utf-8
```

</details>

---

## 📈 **Monitoring & Analytics**

### 📊 **Real-time Monitoring Dashboard**

<details>
<summary><b>🔍 Log Analysis & Debugging</b></summary>

```bash
# Real-time log monitoring
tail -f news_scraping/logs/scrape_vnexpress_log.txt

# Multi-spider log monitoring
tail -f news_scraping/logs/*.txt

# Advanced log analysis
grep "ERROR" news_scraping/logs/*.txt | wc -l  # Count errors
grep "scraped" news_scraping/logs/*.txt | tail -10  # Recent scrapes

# Debug mode with detailed output
scrapy crawl vnexpress -L DEBUG -o debug_output.json 2>&1 | tee debug.log
```

</details>

<details>
<summary><b>🧪 Component Testing</b></summary>

```bash
# Test individual components
cd news_scraping/news_scraping

# Test comment API functionality
python test.py

# Test AWS Bedrock integration
cd ../../bedrocks-apis(ECS)
python testBedrock.py

# Test GCP Vertex AI pipeline
python gcp.py

# Test data processing services
curl -X POST http://localhost:8080/clean -d '{"test": "data"}'
```

</details>

### 📈 **Performance Metrics**

- **📊 Scraping Rate**: Articles per minute across all sources
- **🎯 Success Rate**: Percentage of successful article extractions
- **⚡ Response Time**: Average time per article processing
- **🔄 Data Quality**: Completeness and accuracy scores
- **☁️ Cloud Costs**: AWS Bedrock and GCP usage monitoring

## 📈 Scaling and Customization

### Adding New Spiders

1. Create a new spider file in `news_scraping/spiders/`
2. Inherit from `scrapy.Spider`
3. Define `name`, `allowed_domains`, and `start_urls`
4. Implement `parse()` method
5. Use `NewsScrapingItem` for consistent data structure

### Customizing Search Keywords

Edit the search keywords in individual spider files:
```python
# In thanhnien.py
SEARCH_KEYWORD = "your custom keyword"

# In tuoitrevn.py
SEARCH_KEYWORDS = "your custom keywords"
```

### Adding Data Processing

Implement custom pipelines in `news_scraping/pipelines.py` for:
- Data cleaning and validation
- Database storage
- Data transformation
- Duplicate detection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped and use the data responsibly.

## 🆘 Support

If you encounter issues:

1. Check the log files in `news_scraping/logs/`
2. Verify your Python and dependency versions
3. Ensure Playwright browsers are properly installed
4. Review the website structure (sites may change their HTML structure)

For technical support, please create an issue in the repository with:
- Error messages
- Log file contents
- System information
- Steps to reproduce the issue
